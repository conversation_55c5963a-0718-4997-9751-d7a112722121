#!/usr/bin/env python3
"""
MQTT Test Simulator

This script simulates the MQTT client behavior for testing purposes.
It handles the same topics and message structure as the real MQTT client
but returns successful responses without database or hardware interactions.

Usage:
    python mqtt_test_simulator.py

The script will:
1. Connect to MQTT broker (if configured)
2. Subscribe to all command topics
3. Return successful responses for valid requests
4. Validate request payloads and return appropriate errors for invalid requests
"""

import json
import logging
import random
import string
from typing import Dict, Any, Optional
import paho.mqtt.client as mqtt

# ============================================================================
# CONFIGURATION - Modify these values for your MQTT broker
# ============================================================================
MQTT_HOST="*************"
MQTT_PORT=1883
MQTT_USERNAME="1234"  # Must be string for MQTT client
MQTT_PASSWORD="1234"  # Must be string for MQTT client
MQTT_CLIENT_ID="1234"
# monitor_client_id=server
# monitor_username=server
# monitor_password=12343

MQTT_CONFIG = {
    "MQTT_HOST": MQTT_HOST,           # Your MQTT broker host
    "MQTT_PORT": MQTT_PORT,               # Your MQTT broker port
    "MQTT_USERNAME": MQTT_USERNAME,             # Your MQTT username (None if not required)
    "MQTT_PASSWORD": MQTT_PASSWORD,             # Your MQTT password (None if not required)
    "MQTT_CLIENT_ID": MQTT_CLIENT_ID # Client ID for the simulator
}

# Example configurations:
# For local Mosquitto broker:
# MQTT_CONFIG["MQTT_HOST"] = "localhost"
# MQTT_CONFIG["MQTT_PORT"] = "1883"

# For cloud MQTT broker with authentication:
# MQTT_CONFIG["MQTT_HOST"] = "your-broker.com"
# MQTT_CONFIG["MQTT_PORT"] = "1883"
# MQTT_CONFIG["MQTT_USERNAME"] = "your-username"
# MQTT_CONFIG["MQTT_PASSWORD"] = "your-password"
# ============================================================================

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MQTTTestSimulator:
    """MQTT Test Simulator - Returns successful responses without real operations"""

    def __init__(self):
        # MQTT Configuration from hardcoded values (no .env file needed)
        self.broker = MQTT_CONFIG.get("MQTT_HOST", "localhost")
        self.port = int(MQTT_CONFIG.get("MQTT_PORT", 1883))
        self.username = MQTT_CONFIG.get("MQTT_USERNAME")
        self.password = MQTT_CONFIG.get("MQTT_PASSWORD")
        self.client_id = MQTT_CONFIG.get("MQTT_CLIENT_ID", "test-simulator")

        # Topic patterns for different command types
        self.base_topic = f"devices/{self.client_id}/commands"
        self.base_response_topic = f"devices/{self.client_id}/responses"

        # Command topics (same as real implementation)
        self.electronic_topics = [
            f"{self.base_topic}/electronic/section_open",
            f"{self.base_topic}/electronic/check_doors",
            f"{self.base_topic}/electronic/unlock_service",
            f"{self.base_topic}/electronic/check_service"
        ]

        self.system_topics = [
            f"{self.base_topic}/system/reboot_device"
        ]

        self.sale_topics = [
            f"{self.base_topic}/sale/edit_reservation",
            f"{self.base_topic}/sale/reserve_product",
            f"{self.base_topic}/sale/unreserve_product"
        ]

        self.storage_topics = [
            f"{self.base_topic}/storage/edit_reservation"
        ]

        # All command topics
        self.all_command_topics = (
            self.electronic_topics +
            self.system_topics +
            self.sale_topics +
            self.storage_topics
        )

        # MQTT client instance
        self.client: Optional[mqtt.Client] = None
        self.is_connected = False

    def setup_client(self):
        """Initialize MQTT client with callbacks"""
        if self.client is not None:
            return
            
        self.client = mqtt.Client(mqtt.CallbackAPIVersion.VERSION1, client_id=self.client_id)
        if self.username and self.password:
            self.client.username_pw_set(self.username, self.password)
        self.client.on_connect = self._on_connect
        self.client.on_message = self._on_message
        self.client.on_disconnect = self._on_disconnect

    def _on_connect(self, client, userdata, flags, rc):
        """Callback for when the client connects to the broker"""
        if rc == 0:
            self.is_connected = True
            logger.info("Connected to MQTT broker")

            # Subscribe to all command topics
            for topic in self.all_command_topics:
                client.subscribe(topic)
                logger.info(f"Subscribed to {topic}")
        else:
            self.is_connected = False
            logger.error(f"Failed to connect to MQTT broker, return code: {rc}")

    def _on_disconnect(self, client, userdata, rc):
        """Callback for when the client disconnects from the broker"""
        self.is_connected = False
        logger.info("Disconnected from MQTT broker")

    def _on_message(self, client, userdata, msg):
        """Callback for when a message is received"""
        try:
            payload = json.loads(msg.payload.decode("utf-8"))
            logger.info(f"Received message on {msg.topic}: {payload}")

            # Route message to appropriate handler based on topic
            response = self._handle_command(msg.topic, payload)

            # Send response back to the corresponding response topic
            if response:
                self.publish_response(msg.topic, response)

        except json.JSONDecodeError:
            logger.error(f"Failed to decode JSON: {msg.payload}")
            error_response = {"error": "Invalid JSON format"}
            self.publish_response(msg.topic, error_response)
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            error_response = {"error": str(e)}
            self.publish_response(msg.topic, error_response)

    def _handle_command(self, topic: str, payload: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Handle different command types based on topic"""
        try:
            # Extract command type from topic
            topic_parts = topic.split('/')
            if len(topic_parts) < 4:
                logger.error(f"Invalid topic format: {topic}")
                return {"error": "Invalid topic format"}

            command_category = topic_parts[-2]  # electronic, system, sale, storage
            command_name = topic_parts[-1]     # section_open, check_doors, etc.

            logger.info(f"Processing {command_category}/{command_name} command")

            # Route to appropriate handler
            if command_category == "electronic":
                return self._handle_electronic_command(command_name, payload)
            elif command_category == "system":
                return self._handle_system_command(command_name, payload)
            elif command_category == "sale":
                return self._handle_sale_command(command_name, payload)
            elif command_category == "storage":
                return self._handle_storage_command(command_name, payload)
            else:
                logger.error(f"Unknown command category: {command_category}")
                return {"error": f"Unknown command category: {command_category}"}

        except Exception as e:
            logger.error(f"Error handling command: {e}")
            return {"error": str(e)}

    def _handle_electronic_command(self, command_name: str, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Handle electronic commands - simulate successful responses"""
        logger.info(f"Electronic command: {command_name}")

        if command_name == "section_open":
            return self._simulate_section_open(payload)
        elif command_name == "check_doors":
            return self._simulate_check_doors(payload)
        elif command_name == "unlock_service":
            return self._simulate_unlock_service(payload)
        elif command_name == "check_service":
            return self._simulate_check_service(payload)
        else:
            logger.error(f"Unknown electronic command: {command_name}")
            return {"error": f"Unknown electronic command: {command_name}"}

    def _simulate_section_open(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Simulate opening a specific section"""
        section_id = payload.get("section_id")
        if not section_id:
            return {"success": False, "error": "section_id is required"}

        logger.info(f"Simulating section {section_id} open")
        return {"success": True, "section_id": section_id, "message": f"Section {section_id} opened successfully"}

    def _simulate_check_doors(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Simulate checking door state for a specific section"""
        section_id = payload.get("section_id")
        if not section_id:
            return {"error": "section_id is required"}

        # Simulate random door states for testing
        door_states = ["open", "closed", "locked"]
        simulated_state = random.choice(door_states)

        logger.info(f"Simulating door check for section {section_id}: {simulated_state}")
        return {"door_state": simulated_state, "section_id": section_id}

    def _simulate_unlock_service(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Simulate unlocking service door"""
        logger.info("Simulating service door unlock")
        return {"success": True, "message": "Service door unlocked successfully"}

    def _simulate_check_service(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Simulate checking service door state"""
        # Simulate random service door states
        door_states = ["open", "closed", "locked"]
        simulated_state = random.choice(door_states)

        logger.info(f"Simulating service door check: {simulated_state}")
        return {"door_state": simulated_state, "message": "Service door state checked"}

    def _handle_system_command(self, command_name: str, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Handle system commands - simulate successful responses"""
        logger.info(f"System command: {command_name}")

        if command_name == "reboot_device":
            return self._simulate_reboot_device(payload)
        else:
            logger.error(f"Unknown system command: {command_name}")
            return {"error": f"Unknown system command: {command_name}"}

    def _simulate_reboot_device(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Simulate device reboot"""
        logger.info("Simulating device reboot")
        return {"success": True, "message": "Device reboot initiated successfully"}

    def _handle_sale_command(self, command_name: str, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Handle sale commands - simulate successful responses"""
        logger.info(f"Sale command: {command_name}")

        if command_name == "edit_reservation":
            return self._simulate_edit_sale_reservation(payload)
        elif command_name == "reserve_product":
            return self._simulate_reserve_product(payload)
        elif command_name == "unreserve_product":
            return self._simulate_unreserve_product(payload)
        else:
            logger.error(f"Unknown sale command: {command_name}")
            return {"error": f"Unknown sale command: {command_name}"}

    def _simulate_edit_sale_reservation(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Simulate editing sale reservation"""
        uuid = payload.get("uuid")
        section_id = payload.get("section_id")
        status = payload.get("status")
        price = payload.get("price")

        if not uuid and not section_id:
            return {"success": False, "error": "uuid or section_id is required"}

        identifier = uuid if uuid else f"section_id {section_id}"
        logger.info(f"Simulating edit sale reservation {identifier}: status={status}, price={price}")

        # Check if values are the same (simulate the memory behavior)
        # For testing, we'll assume success unless specifically testing edge cases
        return {"success": True, "message": f"Sale reservation {identifier} updated successfully"}

    def _simulate_reserve_product(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Simulate reserving a product"""
        uuid = payload.get("uuid")
        section_id = payload.get("section_id")

        if not uuid and not section_id:
            return {"success": False, "error": "uuid or section_id is required"}

        identifier = uuid if uuid else f"section_id {section_id}"

        # Generate a random reservation pin for testing
        reservation_pin = ''.join(random.choices(string.digits, k=6))

        logger.info(f"Simulating product reservation {identifier}: pin={reservation_pin}")
        return {"success": True, "reservation_pin": reservation_pin, "message": f"Product {identifier} reserved successfully"}

    def _simulate_unreserve_product(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Simulate unreserving a product"""
        uuid = payload.get("uuid")
        section_id = payload.get("section_id")
        reservation_pin = payload.get("reservation_pin")

        if not uuid and not section_id and not reservation_pin:
            return {"success": False, "error": "uuid, section_id, or reservation_pin is required"}

        identifier = uuid if uuid else (f"section_id {section_id}" if section_id else f"reservation_pin {reservation_pin}")
        logger.info(f"Simulating product unreservation {identifier}")
        return {"success": True, "message": f"Product {identifier} unreserved successfully"}

    def _handle_storage_command(self, command_name: str, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Handle storage commands - simulate successful responses"""
        logger.info(f"Storage command: {command_name}")

        if command_name == "edit_reservation":
            return self._simulate_edit_storage_reservation(payload)
        else:
            logger.error(f"Unknown storage command: {command_name}")
            return {"error": f"Unknown storage command: {command_name}"}

    def _simulate_edit_storage_reservation(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Simulate editing storage reservation"""
        uuid = payload.get("uuid")
        section_id = payload.get("section_id")
        status = payload.get("status")

        if not uuid and not section_id:
            return {"success": False, "error": "uuid or section_id is required"}

        identifier = uuid if uuid else f"section_id {section_id}"
        logger.info(f"Simulating edit storage reservation {identifier}: status={status}")
        return {"success": True, "message": f"Storage reservation {identifier} updated successfully"}

    def publish_response(self, command_topic: str, response: Dict[str, Any]):
        """Publish a response message to the corresponding response topic"""
        if self.client and self.is_connected:
            try:
                # Convert command topic to response topic
                response_topic = command_topic.replace("/commands/", "/responses/")
                
                self.client.publish(response_topic, json.dumps(response))
                logger.info(f"Published to {response_topic}: {response}")
            except Exception as e:
                logger.error(f"Error publishing response: {e}")
        else:
            logger.warning("Cannot publish response - client not connected")

    def start(self):
        """Start the MQTT test simulator"""
        logger.info("Starting MQTT Test Simulator...")

        self.setup_client()

        if not self.broker:
            logger.error("MQTT_HOST not configured in environment")
            return

        try:
            logger.info(f"Connecting to MQTT broker at {self.broker}:{self.port}")
            self.client.connect(self.broker, self.port, 60)

            # Start the network loop (blocking)
            logger.info("Starting MQTT loop...")
            self.client.loop_forever()

        except KeyboardInterrupt:
            logger.info("Stopping MQTT Test Simulator...")
            self.stop()
        except Exception as e:
            logger.error(f"Failed to start MQTT client: {e}")

    def stop(self):
        """Stop the MQTT test simulator"""
        if self.client:
            self.client.disconnect()
            logger.info("MQTT Test Simulator stopped")

def main():
    """Main function to run the MQTT test simulator"""
    print("MQTT Test Simulator")
    print("=" * 50)
    print("This simulator handles the same MQTT topics as the real client")
    print("but returns successful responses without database or hardware operations.")
    print()
    print("Supported topics:")

    simulator = MQTTTestSimulator()

    print("\nElectronic commands:")
    for topic in simulator.electronic_topics:
        print(f"  - {topic}")

    print("\nSystem commands:")
    for topic in simulator.system_topics:
        print(f"  - {topic}")

    print("\nSale commands:")
    for topic in simulator.sale_topics:
        print(f"  - {topic}")

    print("\nStorage commands:")
    for topic in simulator.storage_topics:
        print(f"  - {topic}")

    print(f"\nResponse topic: {simulator.base_response_topic}")
    print("\nPress Ctrl+C to stop the simulator")
    print("=" * 50)
    
    # Start the simulator
    simulator.start()

if __name__ == "__main__":
    main()
